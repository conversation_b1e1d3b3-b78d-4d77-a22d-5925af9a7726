<template>
  <div class="cache-management">
    <el-row :gutter="20">
      <!-- 用户缓存管理 -->
      <el-col :span="12">
        <el-card>
          <div slot="header" class="card-header">
            <span>用户权限缓存管理</span>
            <el-button type="text" size="small" @click="refreshUserCache">刷新</el-button>
          </div>
          
          <el-form size="small">
            <el-form-item label="用户名">
              <el-input
                v-model="userCacheForm.username"
                placeholder="请输入要清除缓存的用户名"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="warning"
                size="small"
                @click="clearUserPermissionCache"
                :loading="userCacheLoading"
                v-hasPermi="['kernel:queryCodePermission:clearUserCache']"
              >
                清除用户缓存
              </el-button>
            </el-form-item>
          </el-form>

          <div class="cache-info">
            <h4>操作说明：</h4>
            <ul>
              <li>清除指定用户的权限缓存数据</li>
              <li>用户下次访问时将重新加载权限</li>
              <li>适用于权限变更后立即生效的场景</li>
            </ul>
          </div>
        </el-card>
      </el-col>

      <!-- 查询编码缓存管理 -->
      <el-col :span="12">
        <el-card>
          <div slot="header" class="card-header">
            <span>查询编码缓存管理</span>
            <el-button type="text" size="small" @click="refreshCodeCache">刷新</el-button>
          </div>
          
          <el-form size="small">
            <el-form-item label="查询编码">
              <el-input
                v-model="codeCacheForm.queryCode"
                placeholder="请输入要清除缓存的查询编码"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="warning"
                size="small"
                @click="clearQueryCodeCache"
                :loading="codeCacheLoading"
                v-hasPermi="['kernel:queryCodePermission:clearCodeCache']"
              >
                清除编码缓存
              </el-button>
            </el-form-item>
          </el-form>

          <div class="cache-info">
            <h4>操作说明：</h4>
            <ul>
              <li>清除指定查询编码的权限缓存</li>
              <li>影响所有使用该编码的用户权限</li>
              <li>适用于编码权限配置变更的场景</li>
            </ul>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 批量缓存管理 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <div slot="header" class="card-header">
            <span>批量缓存管理</span>
          </div>
          
          <div class="batch-operations">
            <el-button
              type="danger"
              icon="el-icon-delete"
              @click="clearAllUserCache"
              :loading="batchLoading"
              v-hasPermi="['kernel:queryCodePermission:clearAllCache']"
            >
              清除所有用户缓存
            </el-button>
            <el-button
              type="danger"
              icon="el-icon-delete"
              @click="clearAllCodeCache"
              :loading="batchLoading"
              v-hasPermi="['kernel:queryCodePermission:clearAllCache']"
            >
              清除所有编码缓存
            </el-button>
            <el-button
              type="danger"
              icon="el-icon-refresh"
              @click="refreshAllCache"
              :loading="batchLoading"
              v-hasPermi="['kernel:queryCodePermission:refreshAllCache']"
            >
              刷新所有缓存
            </el-button>
          </div>

          <el-alert
            title="警告"
            type="warning"
            description="批量清除缓存操作会影响系统性能，请在业务低峰期操作"
            show-icon
            :closable="false"
            style="margin-top: 15px;"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 缓存统计信息 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <div slot="header" class="card-header">
            <span>缓存统计信息</span>
            <el-button type="text" size="small" @click="loadCacheStats">刷新统计</el-button>
          </div>
          
          <el-row :gutter="20" class="cache-stats">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ cacheStats.userCacheCount || 0 }}</div>
                <div class="stat-label">用户缓存数量</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ cacheStats.codeCacheCount || 0 }}</div>
                <div class="stat-label">编码缓存数量</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ cacheStats.totalMemoryUsage || '0MB' }}</div>
                <div class="stat-label">缓存内存占用</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ cacheStats.hitRate || '0%' }}</div>
                <div class="stat-label">缓存命中率</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  clearUserCache,
  clearCodeCache
} from '@/api/kernel/queryCodePermission'

export default {
  name: 'CacheManagement',
  data() {
    return {
      userCacheForm: {
        username: ''
      },
      codeCacheForm: {
        queryCode: ''
      },
      userCacheLoading: false,
      codeCacheLoading: false,
      batchLoading: false,
      cacheStats: {
        userCacheCount: 0,
        codeCacheCount: 0,
        totalMemoryUsage: '0MB',
        hitRate: '0%'
      }
    }
  },
  mounted() {
    this.loadCacheStats()
  },
  methods: {
    /** 清除用户权限缓存 */
    async clearUserPermissionCache() {
      if (!this.userCacheForm.username) {
        this.$message.warning('请输入用户名')
        return
      }

      this.$confirm('确定要清除该用户的权限缓存吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.userCacheLoading = true
        try {
          await clearUserCache(this.userCacheForm.username)
          this.$message.success('用户权限缓存清除成功')
          this.userCacheForm.username = ''
          this.loadCacheStats()
          this.$emit('refresh')
        } catch (error) {
          this.$message.error('清除用户权限缓存失败')
        } finally {
          this.userCacheLoading = false
        }
      })
    },
    /** 清除查询编码缓存 */
    async clearQueryCodeCache() {
      if (!this.codeCacheForm.queryCode) {
        this.$message.warning('请输入查询编码')
        return
      }

      this.$confirm('确定要清除该查询编码的缓存吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.codeCacheLoading = true
        try {
          await clearCodeCache(this.codeCacheForm.queryCode)
          this.$message.success('查询编码缓存清除成功')
          this.codeCacheForm.queryCode = ''
          this.loadCacheStats()
          this.$emit('refresh')
        } catch (error) {
          this.$message.error('清除查询编码缓存失败')
        } finally {
          this.codeCacheLoading = false
        }
      })
    },
    /** 清除所有用户缓存 */
    clearAllUserCache() {
      this.$confirm('确定要清除所有用户的权限缓存吗？此操作可能影响系统性能！', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        this.batchLoading = true
        // 这里需要实现批量清除所有用户缓存的接口
        setTimeout(() => {
          this.$message.success('所有用户缓存清除成功')
          this.batchLoading = false
          this.loadCacheStats()
          this.$emit('refresh')
        }, 2000)
      })
    },
    /** 清除所有编码缓存 */
    clearAllCodeCache() {
      this.$confirm('确定要清除所有查询编码的缓存吗？此操作可能影响系统性能！', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        this.batchLoading = true
        // 这里需要实现批量清除所有编码缓存的接口
        setTimeout(() => {
          this.$message.success('所有编码缓存清除成功')
          this.batchLoading = false
          this.loadCacheStats()
          this.$emit('refresh')
        }, 2000)
      })
    },
    /** 刷新所有缓存 */
    refreshAllCache() {
      this.$confirm('确定要刷新所有缓存吗？此操作可能影响系统性能！', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.batchLoading = true
        // 这里需要实现刷新所有缓存的接口
        setTimeout(() => {
          this.$message.success('所有缓存刷新成功')
          this.batchLoading = false
          this.loadCacheStats()
          this.$emit('refresh')
        }, 3000)
      })
    },
    /** 刷新用户缓存表单 */
    refreshUserCache() {
      this.userCacheForm.username = ''
    },
    /** 刷新编码缓存表单 */
    refreshCodeCache() {
      this.codeCacheForm.queryCode = ''
    },
    /** 加载缓存统计信息 */
    loadCacheStats() {
      // 这里需要实现获取缓存统计信息的接口
      // 暂时使用模拟数据
      this.cacheStats = {
        userCacheCount: 156,
        codeCacheCount: 89,
        totalMemoryUsage: '12.5MB',
        hitRate: '85.6%'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.cache-management {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .cache-info {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 4px;

    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 14px;
    }

    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 5px;
        color: #606266;
        font-size: 12px;
      }
    }
  }

  .batch-operations {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
  }

  .cache-stats {
    .stat-item {
      text-align: center;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 6px;

      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #409eff;
        margin-bottom: 5px;
      }

      .stat-label {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}
</style>
