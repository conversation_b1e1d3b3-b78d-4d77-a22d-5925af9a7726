<template>
  <div class="user-permission-management">
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="用户名">
          <el-input
            v-model="searchForm.username"
            placeholder="请输入用户名"
            clearable
            size="small"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="查询编码">
          <el-input
            v-model="searchForm.queryCode"
            placeholder="请输入查询编码"
            clearable
            size="small"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="small" @click="handleSearch">
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" size="small" @click="resetSearch">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-area">
      <el-button
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="showGrantDialog = true"
        v-hasPermi="['kernel:queryCodePermission:user:grant']"
      >
        授予权限
      </el-button>
      <el-button
        type="success"
        icon="el-icon-s-operation"
        size="small"
        @click="showBatchGrantDialog = true"
        v-hasPermi="['kernel:queryCodePermission:user:batchGrant']"
      >
        批量授予
      </el-button>
      <el-button
        type="danger"
        icon="el-icon-delete"
        size="small"
        :disabled="!multipleSelection.length"
        @click="handleBatchRevoke"
        v-hasPermi="['kernel:queryCodePermission:user:batchRevoke']"
      >
        批量撤销
      </el-button>
    </div>

    <!-- 权限列表表格 -->
    <el-table
      :data="permissionList"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      border
      stripe
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="userId" label="用户ID" width="120" />
      <el-table-column prop="username" label="用户名" width="150" />
      <el-table-column prop="queryCode" label="查询编码" width="200" />
      <el-table-column prop="permissionType" label="权限类型" width="120">
        <template slot-scope="scope">
          <el-tag :type="scope.row.permissionType === 'read' ? 'success' : 'warning'">
            {{ scope.row.permissionType === 'read' ? '只读' : '禁止访问' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180">
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="createBy" label="创建人" width="120" />
      <el-table-column prop="remark" label="备注" width="150" show-overflow-tooltip />
      <el-table-column label="操作" align="center" >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="viewPermissionDetail(scope.row)"
          >
            查看
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="editPermission(scope.row)"
            v-hasPermi="['kernel:queryCodePermission:user:grant']"
          >
            编辑
          </el-button>
          <el-popconfirm
            title="确定要撤销该权限吗？"
            @confirm="handleRevoke(scope.row)"
          >
            <el-button
              slot="reference"
              size="mini"
              type="text"
              icon="el-icon-delete"
              style="color: #f56c6c"
              v-hasPermi="['kernel:queryCodePermission:user:revoke']"
            >
              撤销
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 授予权限对话框 -->
    <GrantPermissionDialog
      :visible.sync="showGrantDialog"
      :permission-data="currentPermission"
      permission-type="user"
      @success="handleGrantSuccess"
    />

    <!-- 批量授予权限对话框 -->
    <BatchGrantDialog
      :visible.sync="showBatchGrantDialog"
      permission-type="user"
      @success="handleBatchGrantSuccess"
    />
  </div>
</template>

<script>
import {
  getUserPermissions,
  revokeUserPermission,
  batchRevokeUserPermissions
} from '@/api/kernel/queryCodePermission'
import GrantPermissionDialog from './GrantPermissionDialog'
import BatchGrantDialog from './BatchGrantDialog'
import { parseTime } from '@/utils/common'

export default {
  name: 'UserPermissionManagement',
  components: {
    GrantPermissionDialog,
    BatchGrantDialog
  },
  data() {
    return {
      loading: false,
      permissionList: [],
      multipleSelection: [],
      total: 0,
      searchForm: {
        username: '',
        queryCode: ''
      },
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        username: null,
        queryCode: null
      },
      showGrantDialog: false,
      showBatchGrantDialog: false,
      currentPermission: {}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    parseTime,
    /** 查询权限列表 */
    async getList() {
      this.loading = true
      try {
        getUserPermissions(this.queryParams).then(response => {
          this.permissionList = response.data
          this.total = response.total
          this.loading = false
        })
        // 这里需要实现分页查询用户权限的接口
        // 暂时使用模拟数据，实际使用时需要调用后端分页接口
        // setTimeout(() => {
        //   this.permissionList = [
        //     {
        //       id: '1',
        //       userId: 'user001',
        //       username: 'admin',
        //       queryCode: 'USER_QUERY',
        //       permissionType: 'read',
        //       createTime: new Date('2024-01-15 10:30:00'),
        //       createBy: 'system',
        //       remark: '管理员默认权限'
        //     },
        //     {
        //       id: '2',
        //       userId: 'user002',
        //       username: 'user1',
        //       queryCode: 'DATA_QUERY',
        //       permissionType: 'write',
        //       createTime: new Date('2024-01-16 14:20:00'),
        //       createBy: 'admin',
        //       remark: '数据查询权限'
        //     }
        //   ]
        //   this.total = 2
        //   this.loading = false
        // }, 500)
      } catch (error) {
        this.loading = false
        this.$message.error('获取权限列表失败')
      }
    },
    /** 搜索按钮操作 */
    handleSearch() {
      this.queryParams.pageNum = 1
      this.queryParams.username = this.searchForm.username
      this.queryParams.queryCode = this.searchForm.queryCode
      this.getList()
    },
    /** 重置按钮操作 */
    resetSearch() {
      this.searchForm = {
        username: '',
        queryCode: ''
      }
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        username: null,
        queryCode: null
      }
      this.getList()
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.multipleSelection = selection
    },
    /** 查看权限详情 */
    viewPermissionDetail(row) {
      this.$alert(`
        <p><strong>权限ID：</strong>${row.id}</p>
        <p><strong>用户ID：</strong>${row.userId}</p>
        <p><strong>用户名：</strong>${row.username}</p>
        <p><strong>查询编码：</strong>${row.queryCode}</p>
        <p><strong>权限类型：</strong>${row.permissionType === 'read' ? '只读' : '禁止访问'}</p>
        <p><strong>创建时间：</strong>${this.$options.filters.parseTime(row.createTime)}</p>
        <p><strong>创建人：</strong>${row.createBy}</p>
        <p><strong>备注：</strong>${row.remark || '无'}</p>
      `, '权限详情', {
        dangerouslyUseHTMLString: true
      })
    },
    /** 编辑权限 */
    editPermission(row) {
      this.currentPermission = { ...row }
      this.showGrantDialog = true
    },
    /** 撤销单个权限 */
    async handleRevoke(row) {
      try {
        await revokeUserPermission(row.username, row.queryCode)
        this.$message.success('权限撤销成功')
        this.getList()
      } catch (error) {
        this.$message.error('权限撤销失败')
      }
    },
    /** 批量撤销权限 */
    async handleBatchRevoke() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择要撤销的权限')
        return
      }

      try {
        const usernames = [...new Set(this.multipleSelection.map(item => item.username))]
        const queryCodes = this.multipleSelection.map(item => item.queryCode)

        for (const username of usernames) {
          const userCodes = this.multipleSelection
            .filter(item => item.username === username)
            .map(item => item.queryCode)

          await batchRevokeUserPermissions({
            username,
            queryCodes: userCodes
          })
        }

        this.$message.success('批量撤销成功')
        this.getList()
      } catch (error) {
        this.$message.error('批量撤销失败')
      }
    },
    /** 授予权限成功回调 */
    handleGrantSuccess() {
      this.showGrantDialog = false
      this.currentPermission = {}
      this.getList()
    },
    /** 批量授予权限成功回调 */
    handleBatchGrantSuccess() {
      this.showBatchGrantDialog = false
      this.getList()
    },
    /** 刷新数据 */
    refreshData() {
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.user-permission-management {
  .search-area {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 15px;

    .search-form {
      margin: 0;
    }
  }

  .action-area {
    margin-bottom: 15px;
    display: flex;
    gap: 10px;
  }
}
</style>
