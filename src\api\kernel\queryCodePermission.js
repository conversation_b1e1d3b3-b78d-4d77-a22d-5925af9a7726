import request from '@/utils/request'

// ==================== 缓存管理接口 ====================

/**
 * 清除用户权限缓存
 */
export function clearUserCache(username) {
  return request({
    url: `/kernel/queryCodePermission/clearUserCache/${username}`,
    method: 'post'
  })
}

/**
 * 清除查询编码权限缓存
 */
export function clearCodeCache(queryCode) {
  return request({
    url: `/kernel/queryCodePermission/clearCodeCache/${queryCode}`,
    method: 'post'
  })
}

/**
 * 检查用户权限
 */
export function checkPermission(username, queryCode) {
  return request({
    url: `/kernel/queryCodePermission/check/${username}/${queryCode}`,
    method: 'get'
  })
}

// ==================== 用户权限管理接口 ====================

/**
 * 授予用户权限
 */
export function grantUserPermission(data) {
  return request({
    url: '/kernel/queryCodePermission/user/grant',
    method: 'post',
    data: data
  })
}

/**
 * 撤销用户权限
 */
export function revokeUserPermission(username, queryCode) {
  return request({
    url: `/kernel/queryCodePermission/user/revoke/${username}/${queryCode}`,
    method: 'delete'
  })
}

/**
 * 获取用户权限列表
 */
export function getUserPermissions(data) {
  return request({
    url: '/kernel/queryCodePermission/user',
    method: 'post',
    data:data
  })
}

/**
 * 批量授予用户权限
 */
export function batchGrantUserPermissions(data) {
  return request({
    url: '/kernel/queryCodePermission/user/batchGrant',
    method: 'post',
    data: data
  })
}

/**
 * 批量撤销用户权限
 */
export function batchRevokeUserPermissions(data) {
  return request({
    url: '/kernel/queryCodePermission/user/batchRevoke',
    method: 'post',
    data: data
  })
}

// ==================== 角色权限管理接口 ====================

/**
 * 授予角色权限
 */
export function grantRolePermission(data) {
  return request({
    url: '/kernel/queryCodePermission/role/grant',
    method: 'post',
    data: data
  })
}

/**
 * 撤销角色权限
 */
export function revokeRolePermission(roleKey, queryCode) {
  return request({
    url: `/kernel/queryCodePermission/role/revoke/${roleKey}/${queryCode}`,
    method: 'delete'
  })
}

/**
 * 获取角色权限列表
 */
export function getRolePermissions(roleKey) {
  return request({
    url: `/kernel/queryCodePermission/role/${roleKey}`,
    method: 'get'
  })
}

/**
 * 批量授予角色权限
 */
export function batchGrantRolePermissions(data) {
  return request({
    url: '/kernel/queryCodePermission/role/batchGrant',
    method: 'post',
    data: data
  })
}

/**
 * 批量撤销角色权限
 */
export function batchRevokeRolePermissions(data) {
  return request({
    url: '/kernel/queryCodePermission/role/batchRevoke',
    method: 'post',
    data: data
  })
}

// ==================== 查询接口 ====================

/**
 * 获取用户可访问的查询编码
 */
export function getUserAccessibleCodes(username) {
  return request({
    url: `/kernel/queryCodePermission/accessible/${username}`,
    method: 'get'
  })
}

/**
 * 获取有权限访问指定编码的用户列表
 */
export function getUsersWithCodeAccess(queryCode) {
  return request({
    url: `/kernel/queryCodePermission/users/${queryCode}`,
    method: 'get'
  })
}
