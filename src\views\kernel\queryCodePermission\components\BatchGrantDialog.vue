<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="700px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="100px"
      size="small"
    >
      <el-form-item
        :label="permissionType === 'user' ? '用户名' : '角色标识'"
        :prop="permissionType === 'user' ? 'username' : 'roleKey'"
      >
        <el-input
          v-if="permissionType === 'user'"
          v-model="form.username"
          placeholder="请输入用户名"
          clearable
        />
        <el-select
          v-else
          v-model="form.roleKey"
          placeholder="请选择角色"
          clearable
          filterable
          style="width: 100%"
        >
          <el-option
            v-for="role in roleOptions"
            :key="role.roleKey"
            :label="role.roleName"
            :value="role.roleKey"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="查询编码" prop="queryCodes">
        <div class="query-codes-container">
          <el-select
            v-model="form.queryCodes"
            multiple
            placeholder="请选择查询编码（可多选）"
            clearable
            filterable
            style="width: 100%"
            :multiple-limit="20"
          >
            <el-option
              v-for="code in queryCodeOptions"
              :key="code.code"
              :label="`${code.code} - ${code.name}`"
              :value="code.code"
            />
          </el-select>
          <div class="selected-codes-info" v-if="form.queryCodes.length">
            已选择 {{ form.queryCodes.length }} 个查询编码
          </div>
        </div>
      </el-form-item>

      <el-form-item label="权限类型" prop="permissionType">
        <el-radio-group v-model="form.permissionType">
          <el-radio label="read">只读权限</el-radio>
          <el-radio label="write">读写权限</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息（可选）"
        />
      </el-form-item>

      <!-- 预览区域 -->
      <el-form-item label="授权预览" v-if="form.queryCodes.length">
        <div class="preview-container">
          <div class="preview-header">
            <span>将为 {{ permissionType === 'user' ? '用户' : '角色' }} 
              <strong>{{ form.username || form.roleKey }}</strong> 
              授予以下权限：
            </span>
          </div>
          <div class="preview-list">
            <el-tag
              v-for="code in form.queryCodes"
              :key="code"
              size="small"
              :type="form.permissionType === 'read' ? 'success' : 'warning'"
              style="margin: 2px 4px 2px 0;"
            >
              {{ code }} ({{ form.permissionType === 'read' ? '只读' : '读写' }})
            </el-tag>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
        批量授予 ({{ form.queryCodes.length }})
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  batchGrantUserPermissions,
  batchGrantRolePermissions
} from '@/api/kernel/queryCodePermission'
import { listRole } from '@/api/system/role'
import { listQuery } from '@/api/kernel/query'

export default {
  name: 'BatchGrantDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    permissionType: {
      type: String,
      required: true,
      validator: value => ['user', 'role'].includes(value)
    }
  },
  data() {
    return {
      dialogVisible: false,
      submitLoading: false,
      form: {
        username: '',
        roleKey: '',
        queryCodes: [],
        permissionType: 'read',
        remark: ''
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        roleKey: [
          { required: true, message: '请选择角色', trigger: 'change' }
        ],
        queryCodes: [
          { required: true, type: 'array', min: 1, message: '请至少选择一个查询编码', trigger: 'change' }
        ],
        permissionType: [
          { required: true, message: '请选择权限类型', trigger: 'change' }
        ]
      },
      roleOptions: [],
      queryCodeOptions: []
    }
  },
  computed: {
    dialogTitle() {
      const typeText = this.permissionType === 'user' ? '用户' : '角色'
      return `批量授予${typeText}权限`
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
        if (val) {
          this.initDialog()
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    /** 初始化对话框 */
    async initDialog() {
      await this.loadOptions()
      this.resetForm()
    },
    /** 加载选项数据 */
    async loadOptions() {
      try {
        // 加载角色选项
        if (this.permissionType === 'role') {
          const roleResponse = await listRole()
          this.roleOptions = roleResponse.data || []
        }
        
        // 加载查询编码选项
        const queryResponse = await listQuery()
        this.queryCodeOptions = (queryResponse.data || []).map(item => ({
          code: item.code,
          name: item.name || item.code
        }))
      } catch (error) {
        console.error('加载选项数据失败:', error)
      }
    },
    /** 提交表单 */
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) return
        
        if (this.form.queryCodes.length === 0) {
          this.$message.warning('请至少选择一个查询编码')
          return
        }
        
        this.submitLoading = true
        try {
          const requestData = {
            queryCodes: this.form.queryCodes,
            permissionType: this.form.permissionType,
            remark: this.form.remark
          }
          
          if (this.permissionType === 'user') {
            requestData.username = this.form.username
            await batchGrantUserPermissions(requestData)
          } else {
            requestData.roleKey = this.form.roleKey
            await batchGrantRolePermissions(requestData)
          }
          
          const typeText = this.permissionType === 'user' ? '用户' : '角色'
          this.$message.success(`${typeText}权限批量授予成功，共授予 ${this.form.queryCodes.length} 个权限`)
          this.$emit('success')
        } catch (error) {
          const typeText = this.permissionType === 'user' ? '用户' : '角色'
          this.$message.error(`${typeText}权限批量授予失败`)
        } finally {
          this.submitLoading = false
        }
      })
    },
    /** 关闭对话框 */
    handleClose() {
      this.dialogVisible = false
      this.resetForm()
    },
    /** 重置表单 */
    resetForm() {
      this.form = {
        username: '',
        roleKey: '',
        queryCodes: [],
        permissionType: 'read',
        remark: ''
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

.query-codes-container {
  .selected-codes-info {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
  }
}

.preview-container {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background: #f8f9fa;

  .preview-header {
    margin-bottom: 10px;
    font-size: 14px;
    color: #303133;
  }

  .preview-list {
    max-height: 120px;
    overflow-y: auto;
  }
}

::v-deep .el-dialog__body {
  padding: 20px 25px;
}

::v-deep .el-select .el-tag {
  max-width: 150px;
}
</style>
