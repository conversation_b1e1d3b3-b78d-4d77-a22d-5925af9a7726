// 查询编码权限管理路由配置
// 将此配置添加到主路由文件中

export const queryCodePermissionRoutes = {
  path: '/kernel/queryCodePermission',
  component: () => import('@/views/kernel/queryCodePermission/index'),
  name: 'QueryCodePermission',
  meta: {
    title: '查询编码权限管理',
    icon: 'el-icon-key',
    roles: ['admin'], // 根据实际需要配置角色权限
    permissions: ['kernel:queryCodePermission:view'] // 根据实际需要配置权限
  },
  children: [
    {
      path: 'user',
      component: () => import('@/views/kernel/queryCodePermission/components/UserPermissionManagement'),
      name: 'UserPermissionManagement',
      meta: {
        title: '用户权限管理',
        permissions: ['kernel:queryCodePermission:user:view']
      },
      hidden: true
    },
    {
      path: 'role',
      component: () => import('@/views/kernel/queryCodePermission/components/RolePermissionManagement'),
      name: 'RolePermissionManagement',
      meta: {
        title: '角色权限管理',
        permissions: ['kernel:queryCodePermission:role:view']
      },
      hidden: true
    },
    {
      path: 'stats',
      component: () => import('@/views/kernel/queryCodePermission/components/PermissionQueryStats'),
      name: 'PermissionQueryStats',
      meta: {
        title: '权限查询统计',
        permissions: ['kernel:queryCodePermission:stats:view']
      },
      hidden: true
    }
  ]
}

// 权限配置说明
export const permissionConfig = {
  // 页面访问权限
  'kernel:queryCodePermission:view': '查询编码权限管理页面访问',
  
  // 用户权限管理
  'kernel:queryCodePermission:user:view': '用户权限查看',
  'kernel:queryCodePermission:user:grant': '用户权限授予',
  'kernel:queryCodePermission:user:revoke': '用户权限撤销',
  'kernel:queryCodePermission:user:batchGrant': '用户权限批量授予',
  'kernel:queryCodePermission:user:batchRevoke': '用户权限批量撤销',
  
  // 角色权限管理
  'kernel:queryCodePermission:role:view': '角色权限查看',
  'kernel:queryCodePermission:role:grant': '角色权限授予',
  'kernel:queryCodePermission:role:revoke': '角色权限撤销',
  'kernel:queryCodePermission:role:batchGrant': '角色权限批量授予',
  'kernel:queryCodePermission:role:batchRevoke': '角色权限批量撤销',
  
  // 权限查询统计
  'kernel:queryCodePermission:stats:view': '权限查询统计查看',
  'kernel:queryCodePermission:check': '权限检查',
  
  // 缓存管理
  'kernel:queryCodePermission:clearUserCache': '清除用户缓存',
  'kernel:queryCodePermission:clearCodeCache': '清除编码缓存',
  'kernel:queryCodePermission:clearAllCache': '清除所有缓存',
  'kernel:queryCodePermission:refreshAllCache': '刷新所有缓存'
}

// 菜单配置示例（用于后台菜单管理）
export const menuConfig = {
  menuName: '查询编码权限管理',
  menuType: 'M', // M目录 C菜单 F按钮
  orderNum: 1,
  path: '/kernel/queryCodePermission',
  component: 'kernel/queryCodePermission/index',
  isFrame: 1,
  isCache: 0,
  visible: 0,
  status: 0,
  icon: 'el-icon-key',
  children: [
    {
      menuName: '用户权限管理',
      menuType: 'C',
      orderNum: 1,
      path: 'user',
      component: 'kernel/queryCodePermission/components/UserPermissionManagement',
      perms: 'kernel:queryCodePermission:user:view'
    },
    {
      menuName: '角色权限管理',
      menuType: 'C',
      orderNum: 2,
      path: 'role',
      component: 'kernel/queryCodePermission/components/RolePermissionManagement',
      perms: 'kernel:queryCodePermission:role:view'
    },
    {
      menuName: '权限查询统计',
      menuType: 'C',
      orderNum: 3,
      path: 'stats',
      component: 'kernel/queryCodePermission/components/PermissionQueryStats',
      perms: 'kernel:queryCodePermission:stats:view'
    },
    // 按钮权限
    {
      menuName: '用户权限授予',
      menuType: 'F',
      orderNum: 1,
      perms: 'kernel:queryCodePermission:user:grant'
    },
    {
      menuName: '用户权限撤销',
      menuType: 'F',
      orderNum: 2,
      perms: 'kernel:queryCodePermission:user:revoke'
    },
    {
      menuName: '角色权限授予',
      menuType: 'F',
      orderNum: 3,
      perms: 'kernel:queryCodePermission:role:grant'
    },
    {
      menuName: '角色权限撤销',
      menuType: 'F',
      orderNum: 4,
      perms: 'kernel:queryCodePermission:role:revoke'
    },
    {
      menuName: '权限检查',
      menuType: 'F',
      orderNum: 5,
      perms: 'kernel:queryCodePermission:check'
    },
    {
      menuName: '缓存管理',
      menuType: 'F',
      orderNum: 6,
      perms: 'kernel:queryCodePermission:clearUserCache'
    }
  ]
}
