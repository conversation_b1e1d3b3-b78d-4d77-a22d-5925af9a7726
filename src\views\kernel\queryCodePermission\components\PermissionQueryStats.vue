<template>
  <div class="permission-query-stats">
    <!-- 查询区域 -->
    <div class="query-area">
      <el-row :gutter="20">
        <!-- 用户权限查询 -->
        <el-col :span="12">
          <el-card class="query-card">
            <div slot="header" class="card-header">
              <span>用户权限查询</span>
              <el-button type="text" @click="refreshUserQuery">刷新</el-button>
            </div>
            <el-form :inline="true" size="small">
              <el-form-item label="用户名">
                <el-input
                  v-model="userQueryForm.username"
                  placeholder="请输入用户名"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="queryUserPermissions">查询</el-button>
              </el-form-item>
            </el-form>
            
            <div v-if="userPermissions.length" class="result-area">
              <h4>用户 "{{ userQueryForm.username }}" 的权限列表：</h4>
              <el-table :data="userPermissions" size="small" border>
                <el-table-column prop="queryCode" label="查询编码" />
                <el-table-column prop="permissionType" label="权限类型" width="100">
                  <template slot-scope="scope">
                    <el-tag size="mini" :type="scope.row.permissionType === 'read' ? 'success' : 'warning'">
                      {{ scope.row.permissionType === 'read' ? '只读' : '读写' }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </el-col>

        <!-- 查询编码权限统计 -->
        <el-col :span="12">
          <el-card class="query-card">
            <div slot="header" class="card-header">
              <span>查询编码权限统计</span>
              <el-button type="text" @click="refreshCodeQuery">刷新</el-button>
            </div>
            <el-form :inline="true" size="small">
              <el-form-item label="查询编码">
                <el-input
                  v-model="codeQueryForm.queryCode"
                  placeholder="请输入查询编码"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="queryCodeStats">查询</el-button>
              </el-form-item>
            </el-form>
            
            <div v-if="codeStats.users || codeStats.roles" class="result-area">
              <h4>查询编码 "{{ codeQueryForm.queryCode }}" 的权限统计：</h4>
              <el-tabs type="border-card" size="small">
                <el-tab-pane label="用户权限" v-if="codeStats.users">
                  <el-tag
                    v-for="user in codeStats.users"
                    :key="user"
                    size="small"
                    style="margin: 2px"
                  >
                    {{ user }}
                  </el-tag>
                  <div class="stats-info">
                    共 {{ codeStats.users.length }} 个用户拥有此权限
                  </div>
                </el-tab-pane>
                <el-tab-pane label="角色权限" v-if="codeStats.roles">
                  <el-tag
                    v-for="role in codeStats.roles"
                    :key="role"
                    type="success"
                    size="small"
                    style="margin: 2px"
                  >
                    {{ role }}
                  </el-tag>
                  <div class="stats-info">
                    共 {{ codeStats.roles.length }} 个角色拥有此权限
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 权限检查工具 -->
    <div class="permission-check-area">
      <el-card>
        <div slot="header" class="card-header">
          <span>权限检查工具</span>
          <el-button type="text" @click="refreshPermissionCheck">刷新</el-button>
        </div>
        <el-form :inline="true" :model="checkForm" size="small">
          <el-form-item label="用户名">
            <el-input
              v-model="checkForm.username"
              placeholder="请输入用户名"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="查询编码">
            <el-input
              v-model="checkForm.queryCode"
              placeholder="请输入查询编码"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="checkUserPermission">检查权限</el-button>
          </el-form-item>
        </el-form>
        
        <div v-if="checkResult !== null" class="check-result">
          <el-alert
            :title="checkResultText"
            :type="checkResult ? 'success' : 'error'"
            :closable="false"
            show-icon
          />
        </div>
      </el-card>
    </div>

    <!-- 权限统计图表 -->
    <div class="stats-charts">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <div slot="header">用户权限分布</div>
            <div id="userStatsChart" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <div slot="header">查询编码使用统计</div>
            <div id="codeStatsChart" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import {
  getUserAccessibleCodes,
  getUsersWithCodeAccess,
  checkPermission
} from '@/api/kernel/queryCodePermission'

export default {
  name: 'PermissionQueryStats',
  data() {
    return {
      userQueryForm: {
        username: ''
      },
      codeQueryForm: {
        queryCode: ''
      },
      checkForm: {
        username: '',
        queryCode: ''
      },
      userPermissions: [],
      codeStats: {
        users: [],
        roles: []
      },
      checkResult: null
    }
  },
  computed: {
    checkResultText() {
      if (this.checkResult === null) return ''
      return this.checkResult 
        ? `用户 "${this.checkForm.username}" 拥有查询编码 "${this.checkForm.queryCode}" 的访问权限`
        : `用户 "${this.checkForm.username}" 没有查询编码 "${this.checkForm.queryCode}" 的访问权限`
    }
  },
  mounted() {
    this.initCharts()
  },
  methods: {
    /** 查询用户权限 */
    async queryUserPermissions() {
      if (!this.userQueryForm.username) {
        this.$message.warning('请输入用户名')
        return
      }
      
      try {
        const response = await getUserAccessibleCodes(this.userQueryForm.username)
        this.userPermissions = response.data.codes.map(code => ({
          queryCode: code,
          permissionType: response.data.permissionType || 'read'
        }))
        
        if (this.userPermissions.length === 0) {
          this.$message.info('该用户暂无任何权限')
        }
      } catch (error) {
        this.$message.error('查询用户权限失败')
        this.userPermissions = []
      }
    },
    /** 查询编码统计 */
    async queryCodeStats() {
      if (!this.codeQueryForm.queryCode) {
        this.$message.warning('请输入查询编码')
        return
      }
      
      try {
        const response = await getUsersWithCodeAccess(this.codeQueryForm.queryCode)
        this.codeStats = {
          users: response.data.users || [],
          roles: response.data.roles || []
        }
        
        if (this.codeStats.users.length === 0 && this.codeStats.roles.length === 0) {
          this.$message.info('该查询编码暂无任何权限配置')
        }
      } catch (error) {
        this.$message.error('查询编码统计失败')
        this.codeStats = { users: [], roles: [] }
      }
    },
    /** 检查用户权限 */
    async checkUserPermission() {
      if (!this.checkForm.username || !this.checkForm.queryCode) {
        this.$message.warning('请输入用户名和查询编码')
        return
      }
      
      try {
        const response = await checkPermission(this.checkForm.username, this.checkForm.queryCode)
        this.checkResult = response.data
      } catch (error) {
        this.$message.error('权限检查失败')
        this.checkResult = false
      }
    },
    /** 刷新用户查询 */
    refreshUserQuery() {
      this.userQueryForm.username = ''
      this.userPermissions = []
    },
    /** 刷新编码查询 */
    refreshCodeQuery() {
      this.codeQueryForm.queryCode = ''
      this.codeStats = { users: [], roles: [] }
    },
    /** 刷新权限检查 */
    refreshPermissionCheck() {
      this.checkForm = { username: '', queryCode: '' }
      this.checkResult = null
    },
    /** 初始化图表 */
    initCharts() {
      // 这里可以使用 ECharts 初始化统计图表
      // 暂时留空，实际使用时可以添加图表展示
    },
    /** 刷新数据 */
    refreshData() {
      this.refreshUserQuery()
      this.refreshCodeQuery()
      this.refreshPermissionCheck()
    }
  }
}
</script>

<style lang="scss" scoped>
.permission-query-stats {
  .query-area {
    margin-bottom: 20px;
  }

  .permission-check-area {
    margin-bottom: 20px;
  }

  .query-card {
    height: 100%;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .result-area {
    margin-top: 15px;
    
    h4 {
      margin-bottom: 10px;
      color: #303133;
    }
  }

  .check-result {
    margin-top: 15px;
  }

  .stats-info {
    margin-top: 10px;
    color: #909399;
    font-size: 12px;
  }

  .stats-charts {
    margin-top: 20px;
  }
}
</style>
