<template>
  <div class="app-container">
    <!-- 页面标题和操作区域 -->
    <div class="page-header">
      <h2>查询编码权限管理</h2>
      <div class="header-actions">
        <el-button 
          type="primary" 
          icon="el-icon-refresh" 
          size="small"
          @click="refreshData"
        >
          刷新数据
        </el-button>
        <el-button 
          type="warning" 
          icon="el-icon-delete" 
          size="small"
          @click="showCacheManagement = true"
        >
          缓存管理
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <el-tabs v-model="activeTab" type="card" class="permission-tabs">
      <!-- 用户权限管理 -->
      <el-tab-pane label="用户权限管理" name="user">
        <UserPermissionManagement ref="userPermission" />
      </el-tab-pane>

      <!-- 角色权限管理 -->
      <el-tab-pane label="角色权限管理" name="role">
        <RolePermissionManagement ref="rolePermission" />
      </el-tab-pane>

      <!-- 权限查询统计 -->
      <el-tab-pane label="权限查询统计" name="query">
        <PermissionQueryStats ref="permissionQuery" />
      </el-tab-pane>
    </el-tabs>

    <!-- 缓存管理对话框 -->
    <el-dialog
      title="缓存管理"
      :visible.sync="showCacheManagement"
      width="600px"
      :close-on-click-modal="false"
    >
      <CacheManagement @refresh="handleCacheRefresh" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="showCacheManagement = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import UserPermissionManagement from './components/UserPermissionManagement'
import RolePermissionManagement from './components/RolePermissionManagement'
import PermissionQueryStats from './components/PermissionQueryStats'
import CacheManagement from './components/CacheManagement'

export default {
  name: 'QueryCodePermissionIndex',
  components: {
    UserPermissionManagement,
    RolePermissionManagement,
    PermissionQueryStats,
    CacheManagement
  },
  data() {
    return {
      activeTab: 'user',
      showCacheManagement: false
    }
  },
  methods: {
    refreshData() {
      // 刷新当前激活标签页的数据
      const currentRef = this.$refs[this.activeTab === 'query' ? 'permissionQuery' : this.activeTab + 'Permission']
      if (currentRef && currentRef.refreshData) {
        currentRef.refreshData()
      }
      this.$message.success('数据刷新成功')
    },
    handleCacheRefresh() {
      this.refreshData()
      this.showCacheManagement = false
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  h2 {
    margin: 0;
    color: #303133;
    font-size: 24px;
    font-weight: 500;
  }

  .header-actions {
    display: flex;
    gap: 10px;
  }
}

.permission-tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;

  ::v-deep .el-tabs__header {
    margin-bottom: 20px;
  }

  ::v-deep .el-tabs__content {
    padding: 0;
  }

  ::v-deep .el-tab-pane {
    min-height: 600px;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
