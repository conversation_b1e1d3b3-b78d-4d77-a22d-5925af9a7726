<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="100px"
      size="small"
    >
      <el-form-item
        :label="permissionType === 'user' ? '用户名' : '角色标识'"
        :prop="permissionType === 'user' ? 'username' : 'roleKey'"
      >
        <el-input
          v-if="permissionType === 'user'"
          v-model="form.username"
          placeholder="请输入用户名"
          clearable
        />
        <el-select
          v-else
          v-model="form.roleKey"
          placeholder="请选择角色"
          clearable
          filterable
          style="width: 100%"
        >
          <el-option
            v-for="role in roleOptions"
            :key="role.roleKey"
            :label="role.roleName"
            :value="role.roleKey"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="查询编码" prop="queryCode">
        <el-select
          v-model="form.queryCode"
          placeholder="请选择查询编码"
          clearable
          filterable
          style="width: 100%"
        >
          <el-option
            v-for="code in queryCodeOptions"
            :key="code.code"
            :label="`${code.code} - ${code.name}`"
            :value="code.code"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="权限类型" prop="permissionType">
        <el-radio-group v-model="form.permissionType">
          <el-radio label="read">只读权限</el-radio>
          <el-radio label="write">读写权限</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="备注">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息（可选）"
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
        {{ isEdit ? '更新' : '授予' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  grantUserPermission,
  grantRolePermission
} from '@/api/kernel/queryCodePermission'
import { listRole } from '@/api/system/role'
import { listQuery } from '@/api/kernel/query'

export default {
  name: 'GrantPermissionDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    permissionType: {
      type: String,
      required: true,
      validator: value => ['user', 'role'].includes(value)
    },
    permissionData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      submitLoading: false,
      form: {
        username: '',
        roleKey: '',
        queryCode: '',
        permissionType: 'read',
        remark: ''
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        roleKey: [
          { required: true, message: '请选择角色', trigger: 'change' }
        ],
        queryCode: [
          { required: true, message: '请选择查询编码', trigger: 'change' }
        ],
        permissionType: [
          { required: true, message: '请选择权限类型', trigger: 'change' }
        ]
      },
      roleOptions: [],
      queryCodeOptions: []
    }
  },
  computed: {
    dialogTitle() {
      const typeText = this.permissionType === 'user' ? '用户' : '角色'
      return this.isEdit ? `编辑${typeText}权限` : `授予${typeText}权限`
    },
    isEdit() {
      return !!(this.permissionData && this.permissionData.id)
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
        if (val) {
          this.initDialog()
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    /** 初始化对话框 */
    async initDialog() {
      await this.loadOptions()
      
      if (this.isEdit) {
        // 编辑模式，填充表单数据
        this.form = {
          username: this.permissionData.username || '',
          roleKey: this.permissionData.roleKey || '',
          queryCode: this.permissionData.queryCode || '',
          permissionType: this.permissionData.permissionType || 'read',
          remark: this.permissionData.remark || ''
        }
      } else {
        // 新增模式，重置表单
        this.resetForm()
      }
    },
    /** 加载选项数据 */
    async loadOptions() {
      try {
        // 加载角色选项
        if (this.permissionType === 'role') {
          const roleResponse = await listRole()
          this.roleOptions = roleResponse.data || []
        }
        
        // 加载查询编码选项
        const queryResponse = await listQuery()
        this.queryCodeOptions = (queryResponse.data || []).map(item => ({
          code: item.code,
          name: item.name || item.code
        }))
      } catch (error) {
        console.error('加载选项数据失败:', error)
      }
    },
    /** 提交表单 */
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) return
        
        this.submitLoading = true
        try {
          const requestData = {
            queryCode: this.form.queryCode,
            permissionType: this.form.permissionType,
            remark: this.form.remark
          }
          
          if (this.permissionType === 'user') {
            requestData.username = this.form.username
            await grantUserPermission(requestData)
          } else {
            requestData.roleKey = this.form.roleKey
            await grantRolePermission(requestData)
          }
          
          const typeText = this.permissionType === 'user' ? '用户' : '角色'
          this.$message.success(`${typeText}权限${this.isEdit ? '更新' : '授予'}成功`)
          this.$emit('success')
        } catch (error) {
          const typeText = this.permissionType === 'user' ? '用户' : '角色'
          this.$message.error(`${typeText}权限${this.isEdit ? '更新' : '授予'}失败`)
        } finally {
          this.submitLoading = false
        }
      })
    },
    /** 关闭对话框 */
    handleClose() {
      this.dialogVisible = false
      this.resetForm()
    },
    /** 重置表单 */
    resetForm() {
      this.form = {
        username: '',
        roleKey: '',
        queryCode: '',
        permissionType: 'read',
        remark: ''
      }
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

::v-deep .el-dialog__body {
  padding: 20px 25px;
}
</style>
